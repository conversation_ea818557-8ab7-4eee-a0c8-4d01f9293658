# Audio Routing Configuration Guide

## Overview

The queue management system now supports automatic audio device routing to ensure that announcement sounds (voice announcements for queue numbers) play through the audio output of the customer display device (TV/monitor) rather than through the host computer's speakers.

## Features

### ✅ Automatic Display Audio Detection
- Automatically detects when the customer display is accessed on an external monitor/TV
- Routes audio to the display device's audio output automatically
- Supports HDMI, DisplayPort, and USB audio devices

### ✅ Manual Device Selection
- Audio device selector component for manual override
- Real-time device detection and switching
- Persistent device preferences

### ✅ Fallback and Recovery
- Automatic fallback to available devices when preferred device is unavailable
- Device health monitoring and recovery
- Graceful degradation to default system audio

### ✅ Multi-Interface Support
- Customer display gets external audio routing
- Cashier panels can use different audio devices
- Independent audio settings per interface

## How It Works

### 1. Device Detection
The system uses the Web Audio API and MediaDevices API to:
- Enumerate available audio output devices
- Identify external display audio devices (HDMI, DisplayPort, etc.)
- Monitor device changes in real-time

### 2. Automatic Routing
When the customer display loads:
1. Detects if running on an external display (screen resolution analysis)
2. Scans for external audio devices (HDMI, display audio, etc.)
3. Automatically routes audio to the best available external device
4. Shows confirmation notification when successful

### 3. Audio APIs Used
- **Web Audio API**: For sound effects and tones
- **Speech Synthesis API**: For voice announcements
- **MediaDevices API**: For device enumeration and selection

## Browser Compatibility

### ✅ Fully Supported
- **Chrome 110+**: Full support including `AudioContext.setSinkId()`
- **Edge 110+**: Full support including `AudioContext.setSinkId()`

### ⚠️ Partial Support
- **Firefox**: Basic functionality, limited device selection
- **Safari**: Basic functionality, no device selection

### ❌ Not Supported
- Internet Explorer
- Older browser versions

## Setup Instructions

### 1. Hardware Setup
1. Connect external monitor/TV via HDMI or DisplayPort
2. Ensure the display has audio capabilities
3. Configure the display as extended or duplicate screen
4. Verify audio is working through the display

### 2. Browser Configuration
1. Use Chrome or Edge for best compatibility
2. Allow microphone permissions when prompted (needed for device enumeration)
3. Ensure audio autoplay is allowed for the site

### 3. System Configuration
1. Open the customer display on the external monitor
2. The system will automatically detect and route audio
3. Use the audio device selector in settings if manual override is needed

## Testing the Configuration

### Using the Audio Test Page
1. Navigate to `/audio-test` in your browser
2. Connect an external display with audio
3. Open the test page on the external display
4. Click "Test Auto-Detect" to verify automatic routing
5. Test different audio functions to confirm routing

### Manual Testing Steps
1. **Test Automatic Detection**:
   - Open customer display on external monitor
   - Look for "Display Audio Active" indicator
   - Verify announcements play through display speakers

2. **Test Manual Selection**:
   - Open settings panel in customer display
   - Select different audio devices
   - Test audio output for each device

3. **Test Fallback Behavior**:
   - Disconnect external display during operation
   - Verify system falls back to default audio
   - Reconnect display and verify auto-recovery

## Configuration Options

### Customer Display Settings
- **Auto-detect Display Audio**: Automatically route to external displays
- **Manual Device Selection**: Choose specific audio output device
- **Audio Device Health Monitoring**: Automatic recovery from device issues

### Persistence
- Device preferences are saved in localStorage
- Separate preferences for customer display vs other interfaces
- Automatic cleanup of invalid device preferences

## Troubleshooting

### Common Issues

#### Audio Not Playing Through External Display
1. **Check Browser Compatibility**: Use Chrome or Edge
2. **Verify Permissions**: Allow microphone access for device enumeration
3. **Check Display Audio**: Ensure display has working audio output
4. **Manual Selection**: Use audio device selector to manually choose device

#### Auto-Detection Not Working
1. **Screen Resolution**: System detects external displays by resolution
2. **Device Labels**: Some devices may not be properly labeled
3. **Manual Override**: Use the audio device selector as fallback

#### Audio Cutting Out or Switching
1. **Device Health Monitoring**: System automatically monitors and recovers
2. **Check Connections**: Ensure stable HDMI/DisplayPort connection
3. **Browser Refresh**: Refresh the page to reinitialize audio system

### Debug Information
Enable browser developer console to see detailed logging:
- Device detection results
- Audio routing attempts
- Error messages and recovery actions

## API Reference

### SoundService Methods

```javascript
// Auto-detect and select display audio
const device = await soundService.autoSelectDisplayAudio();

// Manual device selection
await soundService.setAudioOutputDevice(deviceId);

// Get available devices
const devices = soundService.getAvailableAudioDevices();

// Check if device routing is supported
const isSupported = soundService.isDeviceRoutingSupported();

// Device health monitoring
soundService.startDeviceHealthMonitoring();
```

### AudioDeviceSelector Component

```jsx
<AudioDeviceSelector
  onDeviceChange={handleDeviceChange}
  showAutoDetect={true}
  compact={false}
/>
```

## Security Considerations

- Requires user permission for microphone access (needed for device enumeration)
- Device preferences stored in localStorage (client-side only)
- No sensitive audio data is transmitted or stored
- Follows browser security policies for audio device access

## Performance Impact

- Minimal CPU usage for device monitoring
- No impact on audio quality or latency
- Efficient device enumeration and caching
- Automatic cleanup of unused resources

## Future Enhancements

- Support for audio zones and multi-room setups
- Integration with system audio policies
- Advanced audio routing rules and preferences
- Support for additional audio APIs as they become available
