import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import ForestLakeLogo from '../assets/logo';

const Navigation = () => {
  const location = useLocation();

  const navItems = [
    {
      path: '/customer-display',
      label: '📺 Customer Display',
      description: 'Large screen display for customers',
      color: 'fl-primary-bg hover:bg-green-700'
    },
    {
      path: '/cashier',
      label: '👨‍💼 Cashier Panel',
      description: 'Simple interface for cashiers',
      color: 'fl-accent-bg hover:bg-yellow-600'
    },
    {
      path: '/ticket-generator',
      label: '🎫 Generate Tickets',
      description: 'Create queue tickets for customers',
      color: 'fl-primary-bg hover:bg-green-700'
    },
    {
      path: '/queue-analytics-reporting',
      label: '📊 Analytics',
      description: 'View reports and statistics',
      color: 'fl-accent-bg hover:bg-yellow-600'
    },
    {
      path: '/cashier-control-panel',
      label: '⚙️ Advanced Panel',
      description: 'Full featured control panel',
      color: 'bg-gray-500 hover:bg-gray-600'
    },
    {
      path: '/audio-test',
      label: '🔊 Audio Test',
      description: 'Test audio device routing',
      color: 'bg-indigo-500 hover:bg-indigo-600'
    }
  ];

  // Don't show navigation on customer display or audio test page
  if (location.pathname === '/customer-display' || location.pathname === '/audio-test') {
    return null;
  }

  return (
    <div className="p-4 bg-white border-b border-gray-200 shadow-sm">
      <div className="mx-auto max-w-7xl">
        <div className="flex items-center justify-between mb-4">
          <ForestLakeLogo size="medium" />
          <div className="text-right">
            <div className="text-sm text-gray-600">Queue Management System</div>
            <div className="text-xs text-gray-500">A Better Place™</div>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`
                inline-flex flex-col items-center justify-center p-4 rounded-lg text-white text-center transition-all duration-200 min-w-[140px]
                ${item.color}
                ${location.pathname === item.path ? 'ring-2 ring-white ring-opacity-50 transform scale-105' : ''}
              `}
            >
              <div className="mb-1 text-lg font-semibold">
                {item.label}
              </div>
              <div className="text-xs opacity-90">
                {item.description}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Navigation;
