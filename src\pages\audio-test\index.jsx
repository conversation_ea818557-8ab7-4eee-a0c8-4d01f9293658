import React, { useState, useEffect } from 'react';
import soundService from '../../utils/soundService';
import AudioDeviceSelector from '../../components/AudioDeviceSelector';

const AudioTestPage = () => {
  const [devices, setDevices] = useState([]);
  const [currentDevice, setCurrentDevice] = useState(null);
  const [testResults, setTestResults] = useState([]);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Initialize sound service
    soundService.setEnabled(true);
    soundService.setAnnouncementsEnabled(true);
    soundService.setVolume(0.8);
    
    // Check support
    setIsSupported(soundService.isDeviceRoutingSupported());
    
    // Get devices
    const initialDevices = soundService.getAvailableAudioDevices();
    setDevices(initialDevices);
    setCurrentDevice(soundService.getCurrentAudioDevice());

    // Listen for device changes
    const unsubscribe = soundService.onDeviceChange((newDevices) => {
      setDevices(newDevices);
    });

    return unsubscribe;
  }, []);

  const handleDeviceChange = (device) => {
    setCurrentDevice(device?.deviceId || null);
    addTestResult(`Device changed to: ${device ? soundService.getDeviceDisplayName(device) : 'Default'}`);
  };

  const addTestResult = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { timestamp, message }]);
  };

  const testAnnouncement = () => {
    const testMessage = `Test announcement at ${new Date().toLocaleTimeString()}. Now serving ticket A123 at window 1.`;
    soundService.playAnnouncement(testMessage);
    addTestResult('Played test announcement');
  };

  const testSoundEffect = () => {
    soundService.playSound('callNext');
    addTestResult('Played call next sound effect');
  };

  const testQueueCall = () => {
    soundService.playCallNextCustomer('A456', '2');
    addTestResult('Played queue call (A456 at window 2)');
  };

  const testAutoDetect = async () => {
    try {
      const device = await soundService.autoSelectDisplayAudio();
      if (device) {
        setCurrentDevice(device.deviceId);
        addTestResult(`Auto-detected: ${soundService.getDeviceDisplayName(device)}`);
      } else {
        addTestResult('No external display audio detected');
      }
    } catch (error) {
      addTestResult(`Auto-detect failed: ${error.message}`);
    }
  };

  const testDeviceHealth = async () => {
    try {
      const isHealthy = await soundService.checkCurrentDeviceHealth();
      addTestResult(`Device health check: ${isHealthy ? 'Healthy' : 'Issues detected'}`);
    } catch (error) {
      addTestResult(`Health check failed: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getCurrentDeviceName = () => {
    if (!currentDevice) return 'Default Device';
    const device = devices.find(d => d.deviceId === currentDevice);
    return device ? soundService.getDeviceDisplayName(device) : 'Unknown Device';
  };

  const getExternalDevices = () => {
    return devices.filter(device => soundService.isExternalDisplayDevice(device));
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Audio Routing Test Page</h1>
          <p className="text-gray-600 mb-6">
            Test and validate audio device routing for the queue management system
          </p>

          {!isSupported && (
            <div className="bg-yellow-100 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-yellow-400 rounded-full"></div>
                <span className="font-semibold text-yellow-800">
                  Audio device routing not supported in this browser
                </span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Device Selection */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Audio Device Selection</h2>
              <AudioDeviceSelector
                onDeviceChange={handleDeviceChange}
                showAutoDetect={true}
                compact={false}
              />
              
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="font-semibold text-blue-800">Current Device:</div>
                <div className="text-blue-600">{getCurrentDeviceName()}</div>
              </div>

              {getExternalDevices().length > 0 && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="font-semibold text-green-800">External Devices Detected:</div>
                  <ul className="text-green-600 text-sm mt-1">
                    {getExternalDevices().map(device => (
                      <li key={device.deviceId}>• {soundService.getDeviceDisplayName(device)}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Test Controls */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Audio Tests</h2>
              <div className="space-y-3">
                <button
                  onClick={testAnnouncement}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <span>🔊</span>
                  <span>Test Voice Announcement</span>
                </button>

                <button
                  onClick={testSoundEffect}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <span>🎵</span>
                  <span>Test Sound Effect</span>
                </button>

                <button
                  onClick={testQueueCall}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <span>📢</span>
                  <span>Test Queue Call</span>
                </button>

                <button
                  onClick={testAutoDetect}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <span>🔍</span>
                  <span>Test Auto-Detect</span>
                </button>

                <button
                  onClick={testDeviceHealth}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  <span>🏥</span>
                  <span>Test Device Health</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Test Results</h2>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Clear Results
            </button>
          </div>

          <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-gray-500">No test results yet. Run some tests above.</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-400">[{result.timestamp}]</span> {result.message}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-xl shadow-lg p-8 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Testing Instructions</h2>
          <div className="prose text-gray-600">
            <ol className="list-decimal list-inside space-y-2">
              <li>Connect an external monitor or TV with audio capabilities via HDMI</li>
              <li>Open this page on the external display (drag browser window to external screen)</li>
              <li>Click "Test Auto-Detect" to automatically route audio to the display</li>
              <li>Use the audio device selector to manually choose different output devices</li>
              <li>Test each audio function to verify sound plays through the selected device</li>
              <li>Try disconnecting and reconnecting the external display to test fallback behavior</li>
            </ol>
            
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <strong>Note:</strong> Audio device routing requires a modern browser with Web Audio API support 
              and user permission to access audio devices. Chrome and Edge provide the best compatibility.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioTestPage;
