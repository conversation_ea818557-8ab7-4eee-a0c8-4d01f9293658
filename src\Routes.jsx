import React from "react";
import { Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
// Add your imports here
import QueueStatusTracking from "pages/queue-status-tracking";
import QueueAnalyticsReporting from "pages/queue-analytics-reporting";
import CashierControlPanel from "pages/cashier-control-panel";
import SimpleCashierPanel from "pages/cashier-simple";
import CustomerDisplay from "pages/customer-display";
import AudioTestPage from "pages/audio-test";

import NotFound from "pages/NotFound";

const Routes = () => {
  return (
    <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<QueueStatusTracking />} />
        <Route path="/queue-status-tracking" element={<QueueStatusTracking />} />
        <Route path="/queue-analytics-reporting" element={<QueueAnalyticsReporting />} />
        <Route path="/cashier-control-panel" element={<CashierControlPanel />} />
        <Route path="/cashier" element={<SimpleCashierPanel />} />
        <Route path="/customer-display" element={<CustomerDisplay />} />
        <Route path="/audio-test" element={<AudioTestPage />} />

        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
    </ErrorBoundary>
  );
};

export default Routes;