class SoundService {
  constructor() {
    this.audioContext = null;
    this.sounds = new Map();
    this.enabled = true;
    this.volume = 0.75;
    this.soundEffectsEnabled = true;
    this.announcementsEnabled = true;
    this.currentAudioDevice = null;
    this.availableDevices = [];
    this.deviceChangeListeners = [];

    this.initAudioContext();
    this.loadSounds();
    this.initAudioDeviceDetection();
  }

  initAudioContext() {
    try {
      // Initialize Web Audio API context
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // Resume context on user interaction if suspended
      if (this.audioContext.state === 'suspended') {
        document.addEventListener('click', () => {
          this.audioContext.resume();
        }, { once: true });
      }
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
      this.audioContext = null;
    }
  }

  async initAudioDeviceDetection() {
    try {
      // Request permission to enumerate devices
      await navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
        // Stop the stream immediately as we only needed permission
        stream.getTracks().forEach(track => track.stop());
      });

      // Get available audio output devices
      await this.refreshAudioDevices();

      // Listen for device changes
      navigator.mediaDevices.addEventListener('devicechange', () => {
        this.refreshAudioDevices();
      });

      // Load saved device preference
      this.loadSavedAudioDevice();
    } catch (error) {
      console.warn('Audio device detection not available:', error);
    }
  }

  async refreshAudioDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableDevices = devices.filter(device => device.kind === 'audiooutput');

      // Notify listeners about device changes
      this.deviceChangeListeners.forEach(listener => {
        try {
          listener(this.availableDevices);
        } catch (error) {
          console.warn('Error in device change listener:', error);
        }
      });
    } catch (error) {
      console.warn('Failed to enumerate audio devices:', error);
      this.availableDevices = [];
    }
  }

  onDeviceChange(listener) {
    this.deviceChangeListeners.push(listener);
    // Immediately call with current devices
    listener(this.availableDevices);

    // Return unsubscribe function
    return () => {
      const index = this.deviceChangeListeners.indexOf(listener);
      if (index > -1) {
        this.deviceChangeListeners.splice(index, 1);
      }
    };
  }

  async setAudioOutputDevice(deviceId) {
    try {
      this.currentAudioDevice = deviceId;

      // Set device for Web Audio API context
      if (this.audioContext && this.audioContext.setSinkId) {
        await this.audioContext.setSinkId(deviceId);
      }

      // Save device preference
      this.saveAudioDevice(deviceId);

      console.log('Audio output device set to:', deviceId);
      return true;
    } catch (error) {
      console.warn('Failed to set audio output device:', error);

      // Try to recover by falling back to default device
      try {
        this.currentAudioDevice = null;
        if (this.audioContext && this.audioContext.setSinkId) {
          await this.audioContext.setSinkId('');
        }
        console.log('Recovered by falling back to default audio device');
      } catch (recoveryError) {
        console.error('Failed to recover audio device:', recoveryError);
      }

      return false;
    }
  }

  saveAudioDevice(deviceId) {
    try {
      localStorage.setItem('queuemaster-audio-device', deviceId);
    } catch (error) {
      console.warn('Failed to save audio device preference:', error);
    }
  }

  loadSavedAudioDevice() {
    try {
      const savedDevice = localStorage.getItem('queuemaster-audio-device');
      const customerDisplayPreference = localStorage.getItem('queuemaster-customer-display-audio');

      // For customer display, prefer the customer display specific setting
      const preferredDevice = customerDisplayPreference || savedDevice;

      if (preferredDevice) {
        // Verify device still exists
        const deviceExists = this.availableDevices.some(device => device.deviceId === preferredDevice);
        if (deviceExists) {
          this.setAudioOutputDevice(preferredDevice);
        } else {
          // Remove invalid saved device
          localStorage.removeItem('queuemaster-audio-device');
          if (customerDisplayPreference) {
            localStorage.removeItem('queuemaster-customer-display-audio');
          }

          // Try to fallback to any available external display device
          this.fallbackToExternalDevice();
        }
      }
    } catch (error) {
      console.warn('Failed to load saved audio device:', error);
    }
  }

  async fallbackToExternalDevice() {
    try {
      const externalDevices = await this.detectExternalDisplayAudio();
      if (externalDevices.length > 0) {
        console.log('Falling back to available external device:', externalDevices[0].label);
        await this.setAudioOutputDevice(externalDevices[0].deviceId);
        return externalDevices[0];
      }
    } catch (error) {
      console.warn('Failed to fallback to external device:', error);
    }
    return null;
  }

  saveCustomerDisplayAudioDevice(deviceId) {
    try {
      localStorage.setItem('queuemaster-customer-display-audio', deviceId);
      // Also save as general preference
      this.saveAudioDevice(deviceId);
    } catch (error) {
      console.warn('Failed to save customer display audio device preference:', error);
    }
  }

  getAvailableAudioDevices() {
    return this.availableDevices;
  }

  getCurrentAudioDevice() {
    return this.currentAudioDevice;
  }

  async detectExternalDisplayAudio() {
    try {
      // Look for devices that might be external displays/TVs
      const externalDevices = this.availableDevices.filter(device => {
        const label = device.label.toLowerCase();
        return (
          label.includes('hdmi') ||
          label.includes('display') ||
          label.includes('monitor') ||
          label.includes('tv') ||
          label.includes('external') ||
          label.includes('usb') ||
          (label.includes('digital') && label.includes('audio'))
        );
      });

      return externalDevices;
    } catch (error) {
      console.warn('Failed to detect external display audio:', error);
      return [];
    }
  }

  async autoSelectDisplayAudio() {
    const externalDevices = await this.detectExternalDisplayAudio();

    if (externalDevices.length > 0) {
      // Prefer HDMI devices first, then others
      const hdmiDevice = externalDevices.find(device =>
        device.label.toLowerCase().includes('hdmi')
      );

      const selectedDevice = hdmiDevice || externalDevices[0];
      await this.setAudioOutputDevice(selectedDevice.deviceId);
      return selectedDevice;
    }

    return null;
  }

  async loadSounds() {
    // Generate sounds programmatically using Web Audio API
    if (!this.audioContext) {
      console.warn('Web Audio API not available, sounds disabled');
      return;
    }

    // Create sound generators
    this.sounds.set('callNext', this.createTone.bind(this, [800, 1000], 0.3, 'sine'));
    this.sounds.set('completeService', this.createTone.bind(this, [600, 800, 1000], 0.2, 'sine'));
    this.sounds.set('skipCustomer', this.createTone.bind(this, [400, 300], 0.15, 'square'));
    this.sounds.set('holdCustomer', this.createTone.bind(this, [500, 400, 500], 0.2, 'triangle'));
    this.sounds.set('notification', this.createTone.bind(this, [800], 0.1, 'sine'));
    this.sounds.set('windowOpen', this.createTone.bind(this, [600, 800], 0.2, 'sine'));
    this.sounds.set('windowClose', this.createTone.bind(this, [800, 600], 0.2, 'sine'));
    this.sounds.set('error', this.createTone.bind(this, [300, 250, 200], 0.3, 'sawtooth'));
    this.sounds.set('success', this.createTone.bind(this, [600, 800, 1000, 1200], 0.15, 'sine'));
  }

  createTone(frequencies, duration, waveType = 'sine') {
    if (!this.audioContext) return;

    try {
      const gainNode = this.audioContext.createGain();
      gainNode.connect(this.audioContext.destination);
      gainNode.gain.value = 0;

      const totalDuration = duration * frequencies.length;
      const segmentDuration = duration;

      frequencies.forEach((freq, index) => {
        const oscillator = this.audioContext.createOscillator();
        oscillator.type = waveType;
        oscillator.frequency.value = freq;
        oscillator.connect(gainNode);

        const startTime = this.audioContext.currentTime + (index * segmentDuration);
        const endTime = startTime + segmentDuration;

        // Envelope
        gainNode.gain.setValueAtTime(0, startTime);
        gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, startTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, endTime - 0.01);

        oscillator.start(startTime);
        oscillator.stop(endTime);
      });
    } catch (error) {
      console.warn('Failed to create tone:', error);
    }
  }

  playSound(soundName, options = {}) {
    if (!this.enabled || !this.soundEffectsEnabled) return;

    const soundGenerator = this.sounds.get(soundName);
    if (!soundGenerator) {
      console.warn(`Sound ${soundName} not found`);
      return;
    }

    try {
      // Resume audio context if suspended
      if (this.audioContext && this.audioContext.state === 'suspended') {
        this.audioContext.resume();
      }

      // Call the sound generator function
      if (typeof soundGenerator === 'function') {
        soundGenerator();
      }
    } catch (error) {
      console.warn(`Failed to play sound ${soundName}:`, error);
    }
  }

  async playAnnouncement(text, options = {}) {
    if (!this.enabled || !this.announcementsEnabled) return;

    if ('speechSynthesis' in window) {
      // Cancel any ongoing speech
      speechSynthesis.cancel();

      // For speech synthesis, we need to use HTML Audio element with setSinkId
      // since SpeechSynthesis API doesn't support device selection directly
      await this.playAnnouncementWithAudioElement(text, options);
    } else {
      console.warn('Speech synthesis not supported');
      // Fallback to just playing notification sound
      this.playSound('notification');
    }
  }

  async playAnnouncementWithAudioElement(text, options = {}) {
    try {
      // Play notification sound before announcement
      this.playSound('notification');

      // Create speech synthesis utterance to get audio data
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.volume = options.volume || Math.min(this.volume, 0.8);
      utterance.rate = options.rate || 0.9;
      utterance.pitch = options.pitch || 1;
      utterance.lang = 'en-US';

      // For modern browsers that support setSinkId on SpeechSynthesis
      if (this.currentAudioDevice && speechSynthesis.setSinkId) {
        try {
          await speechSynthesis.setSinkId(this.currentAudioDevice);
        } catch (error) {
          console.warn('Failed to set speech synthesis device:', error);
        }
      }

      setTimeout(() => {
        speechSynthesis.speak(utterance);
      }, 500);

    } catch (error) {
      console.warn('Failed to play announcement with device routing:', error);
      // Fallback to standard speech synthesis
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.volume = options.volume || Math.min(this.volume, 0.8);
      utterance.rate = options.rate || 0.9;
      utterance.pitch = options.pitch || 1;
      utterance.lang = 'en-US';

      setTimeout(() => {
        speechSynthesis.speak(utterance);
      }, 500);
    }
  }

  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  setSoundEffectsEnabled(enabled) {
    this.soundEffectsEnabled = enabled;
  }

  setAnnouncementsEnabled(enabled) {
    this.announcementsEnabled = enabled;
  }

  setEnabled(enabled) {
    this.enabled = enabled;
  }

  // Predefined sound combinations for common actions
  playCallNextCustomer(ticketNumber, windowNumber) {
    this.playSound('callNext');
    
    setTimeout(() => {
      this.playAnnouncement(
        `Now serving ticket ${ticketNumber} at window ${windowNumber}`,
        { rate: 0.9 }
      );
    }, 500);
  }

  playCompleteService() {
    this.playSound('completeService');
    this.playSound('success', { volume: 0.4, fade: true });
  }

  playSkipCustomer() {
    this.playSound('skipCustomer');
  }

  playHoldCustomer() {
    this.playSound('holdCustomer');
  }

  playWindowStatusChange(status) {
    if (status === 'Open') {
      this.playSound('windowOpen');
    } else if (status === 'Closed') {
      this.playSound('windowClose');
    }
  }

  playError() {
    this.playSound('error');
  }

  playNotification() {
    this.playSound('notification');
  }

  // Utility method to check if device routing is supported
  isDeviceRoutingSupported() {
    return !!(this.audioContext && this.audioContext.setSinkId) ||
           !!(window.speechSynthesis && speechSynthesis.setSinkId);
  }

  // Get device info for display
  getDeviceDisplayName(device) {
    if (!device || !device.label) return 'Unknown Device';

    let displayName = device.label;

    // Clean up common device name patterns
    displayName = displayName.replace(/\([^)]*\)/g, '').trim(); // Remove parentheses content
    displayName = displayName.replace(/\s+/g, ' '); // Normalize whitespace

    // Add friendly indicators for external devices
    const label = displayName.toLowerCase();
    if (label.includes('hdmi')) {
      displayName += ' (HDMI)';
    } else if (label.includes('usb')) {
      displayName += ' (USB)';
    } else if (label.includes('bluetooth')) {
      displayName += ' (Bluetooth)';
    }

    return displayName;
  }

  // Check if a device appears to be an external display
  isExternalDisplayDevice(device) {
    if (!device || !device.label) return false;

    const label = device.label.toLowerCase();
    return (
      label.includes('hdmi') ||
      label.includes('display') ||
      label.includes('monitor') ||
      label.includes('tv') ||
      label.includes('external') ||
      (label.includes('digital') && label.includes('audio'))
    );
  }

  // Check if current audio device is still available
  async checkCurrentDeviceHealth() {
    if (!this.currentAudioDevice) return true; // Default device is always available

    try {
      await this.refreshAudioDevices();
      const deviceExists = this.availableDevices.some(
        device => device.deviceId === this.currentAudioDevice
      );

      if (!deviceExists) {
        console.warn('Current audio device no longer available, attempting recovery');
        await this.fallbackToExternalDevice();
        return false;
      }

      return true;
    } catch (error) {
      console.warn('Failed to check device health:', error);
      return false;
    }
  }

  // Periodic health check for audio devices
  startDeviceHealthMonitoring(intervalMs = 30000) {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(() => {
      this.checkCurrentDeviceHealth();
    }, intervalMs);
  }

  stopDeviceHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }
}

// Create singleton instance
const soundService = new SoundService();

export default soundService;