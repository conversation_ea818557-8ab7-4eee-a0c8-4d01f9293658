import React, { useState, useEffect } from 'react';
import useQueue from '../../hooks/useQueue';
import soundService from '../../utils/soundService';
import ForestLakeLogo from '../../assets/logo';
import AudioDeviceSelector from '../../components/AudioDeviceSelector';

const CustomerDisplay = () => {
  const {
    servingQueues,
    waitingQueues,
    stats,
    loading,
    error,
    lastUpdated
  } = useQueue({ autoRefresh: true, refreshInterval: 5000 }); // Refresh every 5 seconds

  const [currentTime, setCurrentTime] = useState(new Date());
  const [lastAnnouncedQueue, setLastAnnouncedQueue] = useState(null);

  // Theme and navigation state
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('customerDisplay-theme');
    return saved ? JSON.parse(saved) : true; // Default to dark mode
  });
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);

  // Audio device state
  const [currentAudioDevice, setCurrentAudioDevice] = useState(null);
  const [audioDeviceDetected, setAudioDeviceDetected] = useState(false);

  // Save theme preference
  useEffect(() => {
    localStorage.setItem('customerDisplay-theme', JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const toggleSideNav = () => {
    setIsSideNavOpen(!isSideNavOpen);
  };

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Play announcement when new queue is called
  useEffect(() => {
    if (servingQueues.length > 0) {
      const latestServing = servingQueues[servingQueues.length - 1];
      if (latestServing && latestServing.queueNumber !== lastAnnouncedQueue) {
        const windowNumber = latestServing.cashierId || '1';
        soundService.playCallNextCustomer(latestServing.queueNumber, windowNumber);
        setLastAnnouncedQueue(latestServing.queueNumber);
      }
    }
  }, [servingQueues, lastAnnouncedQueue]);

  // Initialize sound service and auto-detect display audio
  useEffect(() => {
    soundService.setEnabled(true);
    soundService.setAnnouncementsEnabled(true);
    soundService.setVolume(0.8);

    // Auto-detect and route to external display audio
    const initializeAudio = async () => {
      try {
        // Check if we're likely on an external display
        const isExternalDisplay = await detectExternalDisplay();

        // Wait a moment for devices to be detected
        setTimeout(async () => {
          const externalDevice = await soundService.autoSelectDisplayAudio();
          if (externalDevice) {
            setCurrentAudioDevice(externalDevice);
            setAudioDeviceDetected(true);
            console.log('Auto-selected display audio device:', externalDevice.label);

            // Show a brief notification about audio routing
            if (isExternalDisplay) {
              showAudioRoutingNotification(externalDevice);
            }
          } else {
            console.log('No external display audio device detected, using default');
          }
        }, 1000);
      } catch (error) {
        console.warn('Failed to auto-detect display audio:', error);
      }
    };

    initializeAudio();

    // Start device health monitoring
    soundService.startDeviceHealthMonitoring(30000); // Check every 30 seconds

    // Cleanup on unmount
    return () => {
      soundService.stopDeviceHealthMonitoring();
    };
  }, []);

  // Detect if we're running on an external display
  const detectExternalDisplay = async () => {
    try {
      // Check screen properties that might indicate external display
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;
      const pixelRatio = window.devicePixelRatio;

      // Common external display resolutions
      const externalDisplayResolutions = [
        { width: 1920, height: 1080 }, // Full HD
        { width: 2560, height: 1440 }, // QHD
        { width: 3840, height: 2160 }, // 4K
        { width: 1366, height: 768 },  // HD
        { width: 1680, height: 1050 }, // WSXGA+
        { width: 2560, height: 1600 }, // WQXGA
      ];

      const isCommonExternalResolution = externalDisplayResolutions.some(
        res => res.width === screenWidth && res.height === screenHeight
      );

      // Check if we have multiple screens (if supported)
      let hasMultipleScreens = false;
      if ('getScreenDetails' in window) {
        try {
          const screenDetails = await window.getScreenDetails();
          hasMultipleScreens = screenDetails.screens.length > 1;
        } catch (error) {
          console.log('Screen Details API not available or permission denied');
        }
      }

      // Heuristics for external display detection
      const isLikelyExternal =
        isCommonExternalResolution ||
        hasMultipleScreens ||
        (screenWidth >= 1920 && screenHeight >= 1080); // Large screen assumption

      console.log('Display detection:', {
        screenWidth,
        screenHeight,
        pixelRatio,
        isCommonExternalResolution,
        hasMultipleScreens,
        isLikelyExternal
      });

      return isLikelyExternal;
    } catch (error) {
      console.warn('Failed to detect external display:', error);
      return false;
    }
  };

  // Show notification about audio routing
  const showAudioRoutingNotification = (device) => {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = `
      fixed top-4 right-4 z-50 bg-green-100 border border-green-200 rounded-lg p-4 shadow-lg
      transform transition-all duration-300 translate-x-full
    `;
    notification.innerHTML = `
      <div class="flex items-center space-x-3">
        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
        <div>
          <div class="font-semibold text-green-800">Audio Routed to Display</div>
          <div class="text-sm text-green-600">${soundService.getDeviceDisplayName(device)}</div>
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Animate out and remove
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  };

  // Handle audio device changes
  const handleAudioDeviceChange = (device) => {
    setCurrentAudioDevice(device);
    setAudioDeviceDetected(!!device);

    // Save customer display specific preference
    if (device) {
      soundService.saveCustomerDisplayAudioDevice(device.deviceId);
    }

    console.log('Audio device changed to:', device?.label || 'Default');
  };

  // Get window data for display
  const getWindowData = () => {
    const windows = Array.from({ length: 4 }, (_, i) => ({
      number: i + 1,
      queueNumber: null,
      status: 'CLOSED'
    }));

    servingQueues.forEach(queue => {
      const windowIndex = queue.cashierId ? parseInt(queue.cashierId) - 1 : 0;
      if (windowIndex >= 0 && windowIndex < 4) {
        windows[windowIndex] = {
          number: windowIndex + 1,
          queueNumber: queue.queueNumber,
          status: 'SERVING'
        };
      }
    });

    return windows;
  };

  const windowData = getWindowData();

  return (
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isDarkMode
        ? 'fl-gradient-bg text-white'
        : 'bg-gradient-to-br from-gray-50 to-blue-50 text-gray-900'
    }`}>
      {/* Side Navigation Overlay */}
      {isSideNavOpen && (
        <div
          className="fixed inset-0 z-40 transition-opacity duration-300 bg-black bg-opacity-50"
          onClick={toggleSideNav}
        />
      )}

      {/* Side Navigation Panel */}
      <div className={`
        fixed top-0 right-0 h-full w-80 z-50 transform transition-transform duration-300 ease-in-out
        ${isSideNavOpen ? 'translate-x-0' : 'translate-x-full'}
        ${isDarkMode
          ? 'bg-gray-900 border-l border-gray-700'
          : 'bg-white border-l border-gray-200 shadow-2xl'
        }
      `}>
        <div className="p-6">
          {/* Side Nav Header */}
          <div className="flex items-center justify-between mb-8">
            <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Display Settings
            </h3>
            <button
              onClick={toggleSideNav}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'hover:bg-gray-800 text-gray-400 hover:text-white'
                  : 'hover:bg-gray-100 text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Theme Toggle */}
          <div className="mb-8">
            <label className={`block text-sm font-semibold mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Theme Preference
            </label>
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleTheme}
                className={`
                  relative inline-flex h-8 w-16 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
                  ${isDarkMode
                    ? 'bg-blue-600 focus:ring-blue-500'
                    : 'bg-gray-300 focus:ring-gray-500'
                  }
                `}
              >
                <span className={`
                  inline-block h-6 w-6 transform rounded-full bg-white transition-transform duration-200
                  ${isDarkMode ? 'translate-x-9' : 'translate-x-1'}
                `} />
              </button>
              <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {isDarkMode ? 'Dark Mode' : 'Light Mode'}
              </span>
            </div>
          </div>

          {/* Audio Device Selection */}
          <div className="mb-8">
            <label className={`block text-sm font-semibold mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Audio Output Device
            </label>
            <div className="space-y-3">
              <AudioDeviceSelector
                onDeviceChange={handleAudioDeviceChange}
                showAutoDetect={true}
                compact={true}
                className="w-full"
              />
              {audioDeviceDetected && (
                <div className="flex items-center space-x-2 text-sm text-green-600">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>External display audio detected</span>
                </div>
              )}
            </div>
          </div>

          {/* Additional Settings */}
          <div className="space-y-6">
            <div>
              <label className={`block text-sm font-semibold mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Display Options
              </label>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Auto-refresh
                  </span>
                  <div className={`w-3 h-3 rounded-full ${loading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`} />
                </div>
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Sound notifications
                  </span>
                  <div className={`w-3 h-3 rounded-full ${audioDeviceDetected ? 'bg-green-400' : 'bg-blue-400'}`} />
                </div>
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Audio routing
                  </span>
                  <div className={`w-3 h-3 rounded-full ${audioDeviceDetected ? 'bg-green-400' : 'bg-yellow-400'}`} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className={`backdrop-blur-sm border-b p-6 ${
        isDarkMode
          ? 'bg-black bg-opacity-20 border-white border-opacity-20'
          : 'bg-white bg-opacity-80 border-gray-200 shadow-sm'
      }`}>
        <div className="flex items-center justify-between">
          {/* Hamburger Menu Button */}
          <div className="flex items-center space-x-6">
            <button
              onClick={toggleSideNav}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'hover:bg-white hover:bg-opacity-10 text-white'
                  : 'hover:bg-gray-100 text-gray-700'
              }`}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <ForestLakeLogo
              size="large"
              className={isDarkMode ? 'text-white' : 'text-gray-800'}
            />
            <div>
              <h1 className={`text-4xl font-bold mb-2 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Magandang Araw!
              </h1>
              <p className={`text-lg ${
                isDarkMode ? 'text-white text-opacity-80' : 'text-gray-600'
              }`}>
                Please wait for your number to be called
              </p>
            </div>
          </div>

          <div className="text-right">
            <div className={`text-3xl font-mono font-bold ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              {currentTime.toLocaleTimeString()}
            </div>
            <div className={isDarkMode ? 'text-white text-opacity-80' : 'text-gray-600'}>
              {currentTime.toLocaleDateString()}
            </div>

            {/* Audio Status Indicator */}
            {audioDeviceDetected && (
              <div className="flex items-center justify-end space-x-2 mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className={`text-sm ${isDarkMode ? 'text-green-300' : 'text-green-600'}`}>
                  Display Audio Active
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Connection Status */}
      {error && (
        <div className={`p-4 text-center ${
          isDarkMode
            ? 'bg-red-600 bg-opacity-90 text-white'
            : 'bg-red-100 border border-red-300 text-red-800'
        }`}>
          <div className="text-lg font-semibold">System Offline</div>
          <div className="text-sm">Please wait while we restore connection...</div>
          <div className="mt-2 text-xs opacity-75">Error: {error}</div>
        </div>
      )}

      {/* Main Display Area */}
      <div className="p-8">
        {/* Current Serving Section */}
        <div className="mb-12">
          <h2 className={`text-3xl font-bold text-center mb-8 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            NOW SERVING
          </h2>

          <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
            {windowData.map(window => (
              <div
                key={window.number}
                className={`
                  relative rounded-2xl p-8 text-center transition-all duration-500 transform
                  ${window.status === 'SERVING'
                    ? isDarkMode
                      ? 'fl-accent-bg shadow-2xl scale-105 animate-pulse'
                      : 'bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-2xl scale-105 animate-pulse'
                    : isDarkMode
                      ? 'bg-gray-600 bg-opacity-50'
                      : 'bg-gray-200 border border-gray-300'
                  }
                `}
              >
                <div className={`text-lg font-semibold mb-4 ${
                  window.status === 'SERVING'
                    ? 'text-white'
                    : isDarkMode ? 'text-white' : 'text-gray-700'
                }`}>
                  WINDOW {window.number}
                </div>

                <div className={`
                  text-6xl font-bold font-mono mb-2
                  ${window.status === 'SERVING'
                    ? 'text-white'
                    : isDarkMode ? 'text-gray-400' : 'text-gray-500'
                  }
                `}>
                  {window.queueNumber || '---'}
                </div>

                <div className={`
                  text-sm font-medium
                  ${window.status === 'SERVING'
                    ? 'text-white'
                    : isDarkMode ? 'text-gray-400' : 'text-gray-500'
                  }
                `}>
                  {window.status === 'SERVING' ? 'PLEASE PROCEED' : 'CLOSED'}
                </div>

                {/* Blinking indicator for active windows */}
                {window.status === 'SERVING' && (
                  <div className="absolute w-4 h-4 bg-white rounded-full top-2 right-2 animate-ping"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Queue Statistics */}
        <div className={`backdrop-blur-sm rounded-2xl p-8 ${
          isDarkMode
            ? 'bg-black bg-opacity-20'
            : 'bg-white bg-opacity-80 border border-gray-200 shadow-lg'
        }`}>
          <div className="grid grid-cols-3 gap-8 text-center">
            <div>
              <div className={`text-5xl font-bold mb-2 ${
                isDarkMode ? 'fl-accent' : 'text-blue-600'
              }`}>
                {stats.waiting || 0}
              </div>
              <div className={`text-xl ${
                isDarkMode ? 'text-white text-opacity-80' : 'text-gray-700'
              }`}>
                Customers Waiting
              </div>
            </div>

            <div>
              <div className={`text-5xl font-bold mb-2 ${
                isDarkMode ? 'text-green-300' : 'text-green-600'
              }`}>
                {stats.serving || 0}
              </div>
              <div className={`text-xl ${
                isDarkMode ? 'text-white text-opacity-80' : 'text-gray-700'
              }`}>
                Being Served
              </div>
            </div>

            <div>
              <div className={`text-5xl font-bold mb-2 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                {Math.round(stats.average_wait_time || 0)}m
              </div>
              <div className={`text-xl ${
                isDarkMode ? 'text-white text-opacity-80' : 'text-gray-700'
              }`}>
                Average Wait Time
              </div>
            </div>
          </div>
        </div>

        {/* Next in Queue Preview */}
        {waitingQueues.length > 0 && (
          <div className="mt-12 text-center">
            <h3 className={`text-2xl font-semibold mb-6 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              NEXT IN QUEUE
            </h3>
            <div className="flex justify-center space-x-4">
              {waitingQueues.slice(0, 5).map((queue, index) => (
                <div
                  key={queue.id}
                  className={`
                    rounded-xl p-4 text-center transition-all duration-300
                    ${index === 0
                      ? isDarkMode
                        ? 'ring-2 ring-yellow-400 bg-black bg-opacity-50'
                        : 'ring-2 ring-yellow-500 bg-yellow-50 border border-yellow-200'
                      : isDarkMode
                        ? 'bg-black bg-opacity-30'
                        : 'bg-white bg-opacity-80 border border-gray-200'
                    }
                  `}
                >
                  <div className={`text-2xl font-bold font-mono ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {queue.queueNumber}
                  </div>
                  <div className={`text-sm mt-1 ${
                    isDarkMode ? 'text-white text-opacity-70' : 'text-gray-600'
                  }`}>
                    {queue.serviceType}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className={`fixed bottom-0 left-0 right-0 backdrop-blur-sm p-4 ${
        isDarkMode
          ? 'bg-black bg-opacity-40'
          : 'bg-white bg-opacity-80 border-t border-gray-200'
      }`}>
        <div className={`flex justify-between items-center text-sm ${
          isDarkMode ? 'text-white text-opacity-70' : 'text-gray-600'
        }`}>
          <div>
            {lastUpdated && `Last updated: ${lastUpdated.toLocaleTimeString()}`}
          </div>
          <div className="flex items-center space-x-4">
            <div className={`w-3 h-3 rounded-full ${
              loading ? 'bg-yellow-400 animate-pulse' :
              error ? 'bg-red-400' :
              'bg-green-400'
            }`}></div>
            <span>{loading ? 'Updating...' : error ? 'Offline' : 'Live'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDisplay;
