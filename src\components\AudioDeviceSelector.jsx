import React, { useState, useEffect } from 'react';
import Icon from './AppIcon';
import soundService from '../utils/soundService';

const AudioDeviceSelector = ({ 
  onDeviceChange, 
  showAutoDetect = true, 
  className = '',
  compact = false 
}) => {
  const [devices, setDevices] = useState([]);
  const [currentDevice, setCurrentDevice] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if device routing is supported
    setIsSupported(soundService.isDeviceRoutingSupported());
    
    // Get initial devices and current device
    const initialDevices = soundService.getAvailableAudioDevices();
    setDevices(initialDevices);
    setCurrentDevice(soundService.getCurrentAudioDevice());
    setIsLoading(false);

    // Listen for device changes
    const unsubscribe = soundService.onDeviceChange((newDevices) => {
      setDevices(newDevices);
    });

    return unsubscribe;
  }, []);

  const handleDeviceSelect = async (deviceId) => {
    try {
      const success = await soundService.setAudioOutputDevice(deviceId);
      if (success) {
        setCurrentDevice(deviceId);
        setIsOpen(false);
        
        if (onDeviceChange) {
          const device = devices.find(d => d.deviceId === deviceId);
          onDeviceChange(device);
        }
      }
    } catch (error) {
      console.error('Failed to select audio device:', error);
    }
  };

  const handleAutoDetect = async () => {
    try {
      const selectedDevice = await soundService.autoSelectDisplayAudio();
      if (selectedDevice) {
        setCurrentDevice(selectedDevice.deviceId);
        setIsOpen(false);
        
        if (onDeviceChange) {
          onDeviceChange(selectedDevice);
        }
      }
    } catch (error) {
      console.error('Failed to auto-detect display audio:', error);
    }
  };

  const testCurrentDevice = () => {
    soundService.playAnnouncement('Audio test: This announcement should play through the selected device');
  };

  const getCurrentDeviceName = () => {
    if (!currentDevice) return 'Default Device';
    
    const device = devices.find(d => d.deviceId === currentDevice);
    return device ? soundService.getDeviceDisplayName(device) : 'Unknown Device';
  };

  const getExternalDevices = () => {
    return devices.filter(device => soundService.isExternalDisplayDevice(device));
  };

  if (!isSupported) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        <Icon name="AlertCircle" size={16} className="inline mr-1" />
        Audio device selection not supported in this browser
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        <Icon name="Loader" size={16} className="inline mr-1 animate-spin" />
        Loading audio devices...
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <Icon name="Volume2" size={16} />
          <span className="text-sm font-medium truncate max-w-32">
            {getCurrentDeviceName()}
          </span>
          <Icon name="ChevronDown" size={14} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="p-2">
              {showAutoDetect && (
                <button
                  onClick={handleAutoDetect}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                >
                  <Icon name="Monitor" size={16} />
                  <span>Auto-detect Display Audio</span>
                </button>
              )}
              
              <div className="border-t border-gray-100 mt-2 pt-2">
                {devices.map(device => (
                  <button
                    key={device.deviceId}
                    onClick={() => handleDeviceSelect(device.deviceId)}
                    className={`w-full flex items-center space-x-2 px-3 py-2 text-sm text-left rounded-md transition-colors ${
                      device.deviceId === currentDevice
                        ? 'bg-blue-100 text-blue-700'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <Icon 
                      name={soundService.isExternalDisplayDevice(device) ? "Monitor" : "Speaker"} 
                      size={16} 
                    />
                    <span className="truncate">{soundService.getDeviceDisplayName(device)}</span>
                    {device.deviceId === currentDevice && (
                      <Icon name="Check" size={14} className="text-blue-600 ml-auto" />
                    )}
                  </button>
                ))}
              </div>
              
              <div className="border-t border-gray-100 mt-2 pt-2">
                <button
                  onClick={testCurrentDevice}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded-md transition-colors"
                >
                  <Icon name="Play" size={16} />
                  <span>Test Audio</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full component view
  return (
    <div className={`bg-white border border-gray-200 rounded-xl p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-4">
        <div className="bg-blue-100 p-2 rounded-lg">
          <Icon name="Volume2" size={20} className="text-blue-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">Audio Output Device</h3>
          <p className="text-sm text-gray-500">Select where announcements should play</p>
        </div>
      </div>

      <div className="space-y-3">
        {showAutoDetect && getExternalDevices().length > 0 && (
          <button
            onClick={handleAutoDetect}
            className="w-full flex items-center space-x-3 p-3 border border-blue-200 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <Icon name="Monitor" size={20} className="text-blue-600" />
            <div className="text-left">
              <div className="font-medium text-blue-700">Auto-detect Display Audio</div>
              <div className="text-sm text-blue-600">Automatically route to external display/TV</div>
            </div>
          </button>
        )}

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Available Devices:</label>
          {devices.map(device => (
            <button
              key={device.deviceId}
              onClick={() => handleDeviceSelect(device.deviceId)}
              className={`w-full flex items-center space-x-3 p-3 border rounded-lg transition-colors ${
                device.deviceId === currentDevice
                  ? 'border-blue-300 bg-blue-50'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
            >
              <Icon 
                name={soundService.isExternalDisplayDevice(device) ? "Monitor" : "Speaker"} 
                size={20} 
                className={device.deviceId === currentDevice ? 'text-blue-600' : 'text-gray-400'}
              />
              <div className="flex-1 text-left">
                <div className={`font-medium ${
                  device.deviceId === currentDevice ? 'text-blue-700' : 'text-gray-900'
                }`}>
                  {soundService.getDeviceDisplayName(device)}
                </div>
                {soundService.isExternalDisplayDevice(device) && (
                  <div className="text-sm text-green-600">Recommended for customer display</div>
                )}
              </div>
              {device.deviceId === currentDevice && (
                <Icon name="Check" size={20} className="text-blue-600" />
              )}
            </button>
          ))}
        </div>

        <button
          onClick={testCurrentDevice}
          className="w-full flex items-center justify-center space-x-2 p-3 bg-green-100 border border-green-200 rounded-lg hover:bg-green-200 transition-colors"
        >
          <Icon name="Play" size={20} className="text-green-600" />
          <span className="font-medium text-green-700">Test Current Device</span>
        </button>
      </div>
    </div>
  );
};

export default AudioDeviceSelector;
